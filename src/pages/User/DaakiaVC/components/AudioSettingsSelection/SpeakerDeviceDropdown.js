import React, { useState, useEffect, useCallback, useMemo } from "react";
import PropTypes from "prop-types";
import { Dropdown } from "antd";
import { ReactComponent as SpeakerIcon } from "../settings/icons/SpeakerIcon.svg";
import "./SpeakerDeviceDropdown.scss";

const SpeakerDeviceDropdown = React.memo(function SpeakerDeviceDropdown({
  kind = "audiooutput",
  onActiveDeviceChange,
  initialSelection,
  className = ""
}) {
  const [selectedDevice, setSelectedDevice] = useState(initialSelection || "default");
  const [devices, setDevices] = useState([]);
  const [speakerDeviceLabel, setSpeakerDeviceLabel] = useState("Default Speaker");

  // Get available audio output devices
  useEffect(() => {
    const getDevices = async () => {
      try {
        const deviceList = await navigator.mediaDevices.enumerateDevices();
        const audioOutputDevices = deviceList.filter(device => device.kind === "audiooutput");
        setDevices(audioOutputDevices);
      } catch (error) {
        console.error('Failed to enumerate devices:', error);
      }
    };

    getDevices();

    // Listen for device changes
    const handleDeviceChange = () => getDevices();
    navigator.mediaDevices.addEventListener('devicechange', handleDeviceChange);

    return () => {
      navigator.mediaDevices.removeEventListener('devicechange', handleDeviceChange);
    };
  }, []);

  // Update selected device when initialSelection changes
  useEffect(() => {
    if (initialSelection) {
      setSelectedDevice(initialSelection);
    }
  }, [initialSelection]);

  // Update device label when selectedDevice changes
  useEffect(() => {
    const getDeviceLabel = async () => {
      if (selectedDevice && selectedDevice !== "default") {
        try {
          const selectedDeviceInfo = devices.find(d => d.deviceId === selectedDevice);
          if (selectedDeviceInfo) {
            setSpeakerDeviceLabel(selectedDeviceInfo.label || "Selected Device");
          } else {
            setSpeakerDeviceLabel("Selected Device");
          }
        } catch (error) {
          console.error("Error getting device label:", error);
          setSpeakerDeviceLabel("Unknown Device");
        }
      } else {
        setSpeakerDeviceLabel("Default Speaker");
      }
    };

    getDeviceLabel();
  }, [selectedDevice, devices]);

  const handleDeviceChange = useCallback(async (deviceId) => {
    try {
      setSelectedDevice(deviceId);
      onActiveDeviceChange?.(deviceId);
    } catch (error) {
      console.error('Failed to switch speaker device:', error);
    }
  }, [onActiveDeviceChange]);

  const dropdownContent = useMemo(() => (
    <div className="speaker-device-dropdown-menu">
      {/* Default device option */}
      <button
        key="default"
        className={`device-option ${selectedDevice === "default" ? 'selected' : ''}`}
        onClick={() => handleDeviceChange("default")}
        aria-label="Select Default Speaker"
        type="button"
      >
        Default Speaker
      </button>

      {/* Available devices */}
      {devices.map((device) => {
        const isSelected = selectedDevice === device.deviceId;
        const deviceLabel = device.label || `Speaker ${device.deviceId.slice(0, 8)}...`;

        return (
          <button
            key={device.deviceId}
            className={`device-option ${isSelected ? 'selected' : ''}`}
            onClick={() => handleDeviceChange(device.deviceId)}
            aria-label={`Select ${deviceLabel}`}
            type="button"
          >
            {deviceLabel}
          </button>
        );
      })}
    </div>
  ), [devices, selectedDevice, handleDeviceChange]);

  return (
    <Dropdown
      dropdownRender={() => dropdownContent}
      trigger={['click']}
      placement="top"
      className={className}
      disabled={devices.length === 0}
    >
      <button
        className="speaker-device-dropdown-button"
        type="button"
        aria-label={`Current speaker device: ${speakerDeviceLabel}. Click to change device.`}
        aria-haspopup="listbox"
        aria-expanded="false"
      >
        <SpeakerIcon className="device-icon" />
        <span className="device-name">{speakerDeviceLabel}</span>
        <span className="dropdown-arrow">▼</span>
      </button>
    </Dropdown>
  );
});

SpeakerDeviceDropdown.propTypes = {
  kind: PropTypes.oneOf(['audioinput', 'audiooutput', 'videoinput']),
  onActiveDeviceChange: PropTypes.func,
  initialSelection: PropTypes.string,
  className: PropTypes.string,
};

SpeakerDeviceDropdown.defaultProps = {
  kind: "audiooutput",
  onActiveDeviceChange: undefined,
  initialSelection: undefined,
  className: "",
};

export default SpeakerDeviceDropdown;
